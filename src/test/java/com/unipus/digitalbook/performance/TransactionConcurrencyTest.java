package com.unipus.digitalbook.performance;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 事务并发性能测试
 * 测试长事务对并发请求的影响
 */
@SpringBootTest
@ActiveProfiles("local-test")
@Slf4j
public class TransactionConcurrencyTest {

    private final AtomicInteger completedTasks = new AtomicInteger(0);
    private final AtomicInteger blockedTasks = new AtomicInteger(0);

    /**
     * 测试长事务对并发请求的影响
     */
    @Test
    public void testLongTransactionImpact() throws InterruptedException {
        log.info("开始测试长事务对并发请求的影响");
        
        ExecutorService executor = Executors.newFixedThreadPool(10);
        long startTime = System.currentTimeMillis();
        
        // 启动10个并发任务
        for (int i = 0; i < 10; i++) {
            final int taskId = i;
            CompletableFuture.runAsync(() -> {
                try {
                    if (taskId == 0) {
                        // 第一个任务执行长事务
                        simulateLongTransaction(taskId);
                    } else {
                        // 其他任务执行短事务
                        simulateShortTransaction(taskId);
                    }
                    completedTasks.incrementAndGet();
                } catch (Exception e) {
                    log.error("任务{}执行失败", taskId, e);
                    blockedTasks.incrementAndGet();
                }
            }, executor);
        }
        
        // 等待所有任务完成
        executor.shutdown();
        boolean finished = executor.awaitTermination(30, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        log.info("测试完成:");
        log.info("总耗时: {}ms", totalTime);
        log.info("完成任务数: {}", completedTasks.get());
        log.info("阻塞任务数: {}", blockedTasks.get());
        log.info("是否所有任务完成: {}", finished);
    }

    /**
     * 模拟长事务处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void simulateLongTransaction(int taskId) {
        long startTime = System.currentTimeMillis();
        log.info("任务{} - 开始执行长事务", taskId);
        
        try {
            // 模拟数据库操作
            simulateDatabaseOperation("长事务-数据库操作", 1000);
            
            // 模拟复杂业务逻辑
            simulateComplexBusinessLogic("长事务-业务逻辑", 3000);
            
            // 模拟外部服务调用
            simulateExternalServiceCall("长事务-外部调用", 2000);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("长事务被中断", e);
        }
        
        long endTime = System.currentTimeMillis();
        log.info("任务{} - 长事务完成，耗时: {}ms", taskId, endTime - startTime);
    }

    /**
     * 模拟短事务处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void simulateShortTransaction(int taskId) {
        long startTime = System.currentTimeMillis();
        log.info("任务{} - 开始执行短事务", taskId);
        
        try {
            // 模拟快速数据库操作
            simulateDatabaseOperation("短事务-数据库操作", 100);
            
            // 模拟简单业务逻辑
            simulateComplexBusinessLogic("短事务-业务逻辑", 200);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("短事务被中断", e);
        }
        
        long endTime = System.currentTimeMillis();
        log.info("任务{} - 短事务完成，耗时: {}ms", taskId, endTime - startTime);
    }

    /**
     * 模拟数据库操作
     */
    private void simulateDatabaseOperation(String operation, long delayMs) throws InterruptedException {
        log.debug("执行: {}", operation);
        Thread.sleep(delayMs);
    }

    /**
     * 模拟复杂业务逻辑
     */
    private void simulateComplexBusinessLogic(String operation, long delayMs) throws InterruptedException {
        log.debug("执行: {}", operation);
        Thread.sleep(delayMs);
    }

    /**
     * 模拟外部服务调用
     */
    private void simulateExternalServiceCall(String operation, long delayMs) throws InterruptedException {
        log.debug("执行: {}", operation);
        Thread.sleep(delayMs);
    }

    /**
     * 测试数据库连接池耗尽场景
     */
    @Test
    public void testConnectionPoolExhaustion() throws InterruptedException {
        log.info("开始测试连接池耗尽场景");
        
        ExecutorService executor = Executors.newFixedThreadPool(50);
        long startTime = System.currentTimeMillis();
        
        // 启动50个长事务任务
        for (int i = 0; i < 50; i++) {
            final int taskId = i;
            CompletableFuture.runAsync(() -> {
                try {
                    simulateLongTransaction(taskId);
                    completedTasks.incrementAndGet();
                } catch (Exception e) {
                    log.error("任务{}执行失败: {}", taskId, e.getMessage());
                    blockedTasks.incrementAndGet();
                }
            }, executor);
        }
        
        executor.shutdown();
        boolean finished = executor.awaitTermination(60, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        log.info("连接池测试完成:");
        log.info("总耗时: {}ms", endTime - startTime);
        log.info("完成任务数: {}", completedTasks.get());
        log.info("失败任务数: {}", blockedTasks.get());
        log.info("是否所有任务完成: {}", finished);
    }
}
