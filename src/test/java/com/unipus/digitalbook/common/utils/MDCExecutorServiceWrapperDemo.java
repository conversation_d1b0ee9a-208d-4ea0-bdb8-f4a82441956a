package com.unipus.digitalbook.common.utils;

import org.slf4j.MDC;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * MDCExecutorServiceWrapper 功能演示
 */
public class MDCExecutorServiceWrapperDemo {

    public static void main(String[] args) throws Exception {
        VirtualThreadPoolManager threadPoolManager = new VirtualThreadPoolManager();
        
        // 设置主线程的MDC上下文
        MDC.put("traceId", "demo-trace-123");
        MDC.put("userId", "demo-user-456");
        MDC.put("requestId", "demo-request-789");
        
        System.out.println("=== 主线程 MDC 上下文 ===");
        System.out.println("traceId: " + MDC.get("traceId"));
        System.out.println("userId: " + MDC.get("userId"));
        System.out.println("requestId: " + MDC.get("requestId"));
        
        // 获取批处理executor（已自动包装MDC上下文传递）
        ExecutorService batchExecutor = threadPoolManager.getBatchExecutor();
        
        System.out.println("\n=== 测试 execute() 方法 ===");
        batchExecutor.execute(() -> {
            System.out.println("虚拟线程中的 traceId: " + MDC.get("traceId"));
            System.out.println("虚拟线程中的 userId: " + MDC.get("userId"));
            System.out.println("虚拟线程中的 requestId: " + MDC.get("requestId"));
        });
        
        Thread.sleep(100); // 等待任务完成
        
        System.out.println("\n=== 测试 submit(Callable) 方法 ===");
        var callableResult = batchExecutor.submit(() -> {
            String traceId = MDC.get("traceId");
            String userId = MDC.get("userId");
            return "Callable结果: traceId=" + traceId + ", userId=" + userId;
        });
        
        System.out.println("Callable返回值: " + callableResult.get(5, TimeUnit.SECONDS));
        
        System.out.println("\n=== 测试 submit(Runnable, result) 方法 ===");
        var runnableResult = batchExecutor.submit(() -> {
            System.out.println("Runnable任务中的 traceId: " + MDC.get("traceId"));
            System.out.println("Runnable任务中的 userId: " + MDC.get("userId"));
        }, "自定义返回值");
        
        System.out.println("Runnable返回值: " + runnableResult.get(5, TimeUnit.SECONDS));
        
        System.out.println("\n=== 测试 submit(Runnable) 方法 ===");
        var simpleRunnableResult = batchExecutor.submit(() -> {
            System.out.println("简单Runnable任务中的 requestId: " + MDC.get("requestId"));
        });
        
        simpleRunnableResult.get(5, TimeUnit.SECONDS);
        System.out.println("简单Runnable任务完成");
        
        System.out.println("\n=== 验证主线程MDC未被影响 ===");
        System.out.println("主线程 traceId: " + MDC.get("traceId"));
        System.out.println("主线程 userId: " + MDC.get("userId"));
        System.out.println("主线程 requestId: " + MDC.get("requestId"));
        
        // 关闭线程池
        threadPoolManager.destroy();
        
        System.out.println("\n=== 演示完成 ===");
    }
}
