package com.unipus.digitalbook.common.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VirtualThreadPoolManager MDC上下文传递测试
 */
class VirtualThreadPoolManagerMDCTest {

    private VirtualThreadPoolManager threadPoolManager;

    @BeforeEach
    void setUp() {
        threadPoolManager = new VirtualThreadPoolManager();
        // 清理MDC上下文
        MDC.clear();
    }

    @Test
    void testGetBatchExecutorWithMDCContext() throws Exception {
        // 设置MDC上下文
        String traceId = "test-trace-123";
        String userId = "user-456";
        MDC.put("traceId", traceId);
        MDC.put("userId", userId);

        // 获取批处理executor
        ExecutorService batchExecutor = threadPoolManager.getBatchExecutor();

        // 用于存储虚拟线程中的MDC值
        AtomicReference<String> threadTraceId = new AtomicReference<>();
        AtomicReference<String> threadUserId = new AtomicReference<>();

        // 提交任务到批处理executor
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            // 在虚拟线程中获取MDC值
            threadTraceId.set(MDC.get("traceId"));
            threadUserId.set(MDC.get("userId"));
        }, batchExecutor);

        // 等待任务完成
        future.get(5, TimeUnit.SECONDS);

        // 验证MDC上下文是否正确传递
        assertEquals(traceId, threadTraceId.get(), "traceId should be passed to virtual thread");
        assertEquals(userId, threadUserId.get(), "userId should be passed to virtual thread");
    }

    @Test
    void testGetMainExecutorWithMDCContext() throws Exception {
        // 设置MDC上下文
        String traceId = "main-trace-789";
        String sessionId = "session-abc";
        MDC.put("traceId", traceId);
        MDC.put("sessionId", sessionId);

        // 获取主executor
        ExecutorService mainExecutor = threadPoolManager.getMainExecutor();

        // 用于存储虚拟线程中的MDC值
        AtomicReference<String> threadTraceId = new AtomicReference<>();
        AtomicReference<String> threadSessionId = new AtomicReference<>();

        // 提交任务到主executor
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            // 在虚拟线程中获取MDC值
            threadTraceId.set(MDC.get("traceId"));
            threadSessionId.set(MDC.get("sessionId"));
        }, mainExecutor);

        // 等待任务完成
        future.get(5, TimeUnit.SECONDS);

        // 验证MDC上下文是否正确传递
        assertEquals(traceId, threadTraceId.get(), "traceId should be passed to virtual thread");
        assertEquals(sessionId, threadSessionId.get(), "sessionId should be passed to virtual thread");
    }

    @Test
    void testExecutorSubmitWithMDCContext() throws Exception {
        // 设置MDC上下文
        String requestId = "req-submit-123";
        MDC.put("requestId", requestId);

        // 获取批处理executor
        ExecutorService batchExecutor = threadPoolManager.getBatchExecutor();

        // 用于存储虚拟线程中的MDC值
        AtomicReference<String> threadRequestId = new AtomicReference<>();

        // 使用submit方法提交任务
        var future = batchExecutor.submit(() -> {
            threadRequestId.set(MDC.get("requestId"));
            return "task completed";
        });

        // 等待任务完成
        String result = future.get(5, TimeUnit.SECONDS);

        // 验证结果和MDC上下文
        assertEquals("task completed", result);
        assertEquals(requestId, threadRequestId.get(), "requestId should be passed to virtual thread");
    }

    @Test
    void testExecutorExecuteWithMDCContext() throws Exception {
        // 设置MDC上下文
        String operationId = "op-execute-456";
        MDC.put("operationId", operationId);

        // 获取批处理executor
        ExecutorService batchExecutor = threadPoolManager.getBatchExecutor();

        // 用于存储虚拟线程中的MDC值
        AtomicReference<String> threadOperationId = new AtomicReference<>();
        CompletableFuture<Void> completionSignal = new CompletableFuture<>();

        // 使用execute方法提交任务
        batchExecutor.execute(() -> {
            threadOperationId.set(MDC.get("operationId"));
            completionSignal.complete(null);
        });

        // 等待任务完成
        completionSignal.get(5, TimeUnit.SECONDS);

        // 验证MDC上下文
        assertEquals(operationId, threadOperationId.get(), "operationId should be passed to virtual thread");
    }

    @Test
    void testMDCContextIsolation() throws Exception {
        // 设置初始MDC上下文
        String mainTraceId = "main-trace-isolation";
        MDC.put("traceId", mainTraceId);

        ExecutorService batchExecutor = threadPoolManager.getBatchExecutor();

        // 用于存储不同线程中的MDC值
        AtomicReference<String> thread1TraceId = new AtomicReference<>();
        AtomicReference<String> thread2TraceId = new AtomicReference<>();

        // 第一个任务
        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
            thread1TraceId.set(MDC.get("traceId"));
            // 在虚拟线程中修改MDC
            MDC.put("traceId", "modified-in-thread1");
        }, batchExecutor);

        // 第二个任务
        CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
            thread2TraceId.set(MDC.get("traceId"));
        }, batchExecutor);

        // 等待所有任务完成
        CompletableFuture.allOf(future1, future2).get(5, TimeUnit.SECONDS);

        // 验证MDC上下文隔离
        assertEquals(mainTraceId, thread1TraceId.get(), "Thread1 should get original traceId");
        assertEquals(mainTraceId, thread2TraceId.get(), "Thread2 should get original traceId");

        // 验证主线程的MDC没有被影响
        assertEquals(mainTraceId, MDC.get("traceId"), "Main thread MDC should not be affected");
    }

    @Test
    void testDirectExecutorServiceMethods() throws Exception {
        // 设置MDC上下文
        String testTraceId = "direct-executor-test";
        MDC.put("traceId", testTraceId);

        ExecutorService batchExecutor = threadPoolManager.getBatchExecutor();

        // 测试 execute 方法
        AtomicReference<String> executeTraceId = new AtomicReference<>();
        CompletableFuture<Void> executeSignal = new CompletableFuture<>();

        batchExecutor.execute(() -> {
            executeTraceId.set(MDC.get("traceId"));
            executeSignal.complete(null);
        });

        executeSignal.get(5, TimeUnit.SECONDS);
        assertEquals(testTraceId, executeTraceId.get(), "execute method should pass MDC context");

        // 测试 submit(Callable) 方法
        var callableResult = batchExecutor.submit(() -> {
            return MDC.get("traceId");
        });

        String callableTraceId = callableResult.get(5, TimeUnit.SECONDS);
        assertEquals(testTraceId, callableTraceId, "submit(Callable) should pass MDC context");

        // 测试 submit(Runnable, result) 方法
        AtomicReference<String> runnableWithResultTraceId = new AtomicReference<>();
        var runnableWithResultFuture = batchExecutor.submit(() -> {
            runnableWithResultTraceId.set(MDC.get("traceId"));
        }, "test-result");

        String result = runnableWithResultFuture.get(5, TimeUnit.SECONDS);
        assertEquals("test-result", result);
        assertEquals(testTraceId, runnableWithResultTraceId.get(), "submit(Runnable, result) should pass MDC context");
    }
}
