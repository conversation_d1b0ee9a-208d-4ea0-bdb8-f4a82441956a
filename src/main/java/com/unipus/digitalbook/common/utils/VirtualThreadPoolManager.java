package com.unipus.digitalbook.common.utils;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 虚拟线程池管理器
 * 提供统一的虚拟线程池管理和优雅关闭机制
 */
@Component
@Slf4j
public class VirtualThreadPoolManager implements DisposableBean {

    // 关闭超时时间（秒），默认30秒
    private static final long shutdownTimeoutSeconds = 30L;

    // 强制关闭超时时间（秒），默认10秒
    private static final long forceShutdownTimeoutSeconds = 10L;

    // 包装后的主虚拟线程池 - 自动传递MDC上下文
    private final ExecutorService wrappedMainVirtualThreadPool;

    // 包装后的批处理虚拟线程池 - 自动传递MDC上下文
    private final ExecutorService wrappedBatchVirtualThreadPool;

    public VirtualThreadPoolManager() {
        // 创建包装后的ExecutorService，自动传递MDC上下文
        this.wrappedMainVirtualThreadPool = new MDCExecutorServiceWrapper(Executors.newVirtualThreadPerTaskExecutor());
        this.wrappedBatchVirtualThreadPool = new MDCExecutorServiceWrapper(Executors.newVirtualThreadPerTaskExecutor());
    }

    /**
     * 获取主虚拟线程池（自动传递MDC上下文）
     */
    public ExecutorService getMainExecutor() {
        return wrappedMainVirtualThreadPool;
    }

    /**
     * 获取批处理虚拟线程池（自动传递MDC上下文）
     */
    public ExecutorService getBatchExecutor() {
        return wrappedBatchVirtualThreadPool;
    }

    /**
     * 提交异步任务到主线程池
     */
    public void executeAsync(Runnable task) {
        mainVirtualThreadPool.execute(wrapWithMDC(task));
    }

    /**
     * 提交异步任务到主线程池并返回CompletableFuture
     * @param supplier 任务逻辑
     * @return CompletableFuture 对象
     */
    public <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(wrapWithMDC(supplier), mainVirtualThreadPool);
    }

    /**
     * 提交异步任务到批处理线程池
     * @param task 任务
     */
    public void executeBatchAsync(Runnable task) {
        batchVirtualThreadPool.execute(wrapWithMDC(task));
    }

    /**
     * 提交异步任务到批处理线程池并返回CompletableFuture
     * @param supplier 任务逻辑
     * @return CompletableFuture 对象
     */
    public <T> CompletableFuture<T> supplyBatchAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(wrapWithMDC(supplier), batchVirtualThreadPool);
    }

    /**
     * 优雅关闭所有线程池
     */
    @PreDestroy
    @Override
    public void destroy() {
        // 关闭主线程池
        shutdownExecutorGracefully("Main virtual thread pool", mainVirtualThreadPool);

        // 关闭批处理线程池
        shutdownExecutorGracefully("Batch virtual thread pool", batchVirtualThreadPool);

        log.info("Virtual thread pool shutdown completed");
    }

    /**
     * 优雅关闭单个ExecutorService
     * @param poolName 线程池名称
     * @param executor 线程池对象
     */
    private void shutdownExecutorGracefully(String poolName, ExecutorService executor) {
        try {
            // 1. 停止接收新任务
            executor.shutdown();

            // 2. 等待现有任务完成
            if (executor.awaitTermination(shutdownTimeoutSeconds, TimeUnit.SECONDS)) {
                return;
            }
            log.warn("{} failed to shutdown within {} seconds, attempting force shutdown", poolName, shutdownTimeoutSeconds);

            // 3. 强制关闭
            executor.shutdownNow();

            // 4. 再次等待强制关闭完成
            if (!executor.awaitTermination(forceShutdownTimeoutSeconds, TimeUnit.SECONDS)) {
                log.error("{} force shutdown failed", poolName);
            }
        } catch (InterruptedException e) {
            log.warn("{} shutdown process was interrupted", poolName);
            Thread.currentThread().interrupt();
            // 确保强制关闭
            executor.shutdownNow();
        }
    }

    /**
     * 包装Runnable任务，传递MDC上下文到虚拟线程
     * @param task 原始任务
     * @return 包装后的任务
     */
    private Runnable wrapWithMDC(Runnable task) {
        // 在主线程中获取MDC上下文
        Map<String, String> contextMap = MDC.getCopyOfContextMap();

        return () -> {
            // 在虚拟线程中设置MDC上下文
            if (contextMap != null) {
                MDC.setContextMap(contextMap);
            }
            try {
                // 执行原始任务
                task.run();
            } finally {
                // 清理MDC上下文，防止内存泄漏
                MDC.clear();
            }
        };
    }

    /**
     * 包装Supplier任务，传递MDC上下文到虚拟线程
     * @param supplier 原始任务
     * @return 包装后的任务
     */
    private <T> Supplier<T> wrapWithMDC(Supplier<T> supplier) {
        // 在主线程中获取MDC上下文
        Map<String, String> contextMap = MDC.getCopyOfContextMap();

        return () -> {
            // 在虚拟线程中设置MDC上下文
            if (contextMap != null) {
                MDC.setContextMap(contextMap);
            }
            try {
                // 执行原始任务并返回结果
                return supplier.get();
            } finally {
                // 清理MDC上下文，防止内存泄漏
                MDC.clear();
            }
        };
    }

    /**
     * ExecutorService包装器，自动为所有提交的任务传递MDC上下文
     */
    private class MDCExecutorServiceWrapper implements ExecutorService {
        private final ExecutorService delegate;

        public MDCExecutorServiceWrapper(ExecutorService delegate) {
            this.delegate = delegate;
        }

        @Override
        public void shutdown() {
            delegate.shutdown();
        }

        @Override
        public List<Runnable> shutdownNow() {
            return delegate.shutdownNow();
        }

        @Override
        public boolean isShutdown() {
            return delegate.isShutdown();
        }

        @Override
        public boolean isTerminated() {
            return delegate.isTerminated();
        }

        @Override
        public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
            return delegate.awaitTermination(timeout, unit);
        }

        @NotNull
        @Override
        public <T> Future<T> submit(@NotNull Callable<T> task) {
            return delegate.submit(wrapWithMDC(task));
        }

        @NotNull
        @Override
        public <T> Future<T> submit(@NotNull Runnable task, T result) {
            return delegate.submit(wrapWithMDC(task), result);
        }

        @NotNull
        @Override
        public Future<?> submit(Runnable task) {
            return delegate.submit(wrapWithMDC(task));
        }

        @Override
        public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
            return delegate.invokeAll(wrapWithMDC(tasks));
        }

        @Override
        public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException {
            return delegate.invokeAll(wrapWithMDC(tasks), timeout, unit);
        }

        @Override
        public <T> T invokeAny(Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
            return delegate.invokeAny(wrapWithMDC(tasks));
        }

        @Override
        public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
            return delegate.invokeAny(wrapWithMDC(tasks), timeout, unit);
        }

        @Override
        public void execute(Runnable command) {
            delegate.execute(wrapWithMDC(command));
        }

        /**
         * 包装Callable任务，传递MDC上下文到虚拟线程
         */
        private <T> Callable<T> wrapWithMDC(Callable<T> callable) {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                try {
                    return callable.call();
                } finally {
                    MDC.clear();
                }
            };
        }

        /**
         * 包装Callable任务集合，传递MDC上下文到虚拟线程
         */
        private <T> Collection<Callable<T>> wrapWithMDC(Collection<? extends Callable<T>> tasks) {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return tasks.stream()
                    .map(task -> (Callable<T>) () -> {
                        if (contextMap != null) {
                            MDC.setContextMap(contextMap);
                        }
                        try {
                            return task.call();
                        } finally {
                            MDC.clear();
                        }
                    })
                    .toList();
        }
    }

}
